import React from "react";
import { useNavigate } from "react-router-dom";
import {
  Layout,
  Row,
  Col,
  Typography,
  Divider,
  Space,
  Input,
  Button,
  Form,
  List,
  Avatar,
  Card,
} from "antd";
import {
  EnvironmentOutlined,
  PhoneOutlined,
  MailOutlined,
  GoldOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  ShoppingOutlined,
  ContactsOutlined,
  FacebookOutlined,
  InstagramOutlined,
  TwitterOutlined,
  SendOutlined,
  ClockCircleOutlined,
  ToolOutlined,
  ExperimentOutlined,
  HeartFilled,
  RightOutlined,
} from "@ant-design/icons";
import styled, { keyframes } from "styled-components";
import RamavatargemsLogo from "./logo/RamavatargemsLogo";
import { useTheme } from "../context/ThemeContext";

const { Footer: AntFooter } = Layout;
const { Title, Text, Link: AntLink, Paragraph } = Typography;

// Animations
const float = keyframes`
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
`;

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;

// Main Footer Container
const StyledFooter = styled(AntFooter)`
  padding: 0;
  background-color: transparent;
  position: relative;
  z-index: 1;
`;

// Top Footer with main content
const TopFooter = styled.div`
  background: var(--gradient-primary);
  padding: var(--spacing-16) 0 var(--spacing-12);
  color: #fff;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/images/manufacturing/diamond_pattern.png');
    background-size: 300px;
    opacity: 0.05;
    z-index: 0;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: var(--gradient-accent-2);
    opacity: 0.2;
    filter: blur(50px);
    z-index: 0;
  }
`;

const FooterContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  position: relative;
  z-index: 1;
`;

// Bottom Footer with copyright
const BottomFooter = styled.div`
  background-color: var(--color-primary-dark);
  padding: var(--spacing-4) 0;
  color: rgba(255, 255, 255, 0.7);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right,
      transparent 0%,
      rgba(255, 255, 255, 0.2) 50%,
      transparent 100%
    );
  }
`;

// Section Title
const FooterTitle = styled(Title)`
  color: white !important;
  margin-bottom: var(--spacing-6) !important;
  position: relative;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
  display: inline-block;

  &::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--gradient-rainbow);
    border-radius: 3px;
  }
`;

// Logo section
const LogoSection = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-6);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
`;

// Footer text
const FooterText = styled(Paragraph)`
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: var(--spacing-6) !important;
  font-size: 1rem !important;
  line-height: 1.6 !important;
  max-width: 90%;
`;

// Social icons
const SocialIcons = styled.div`
  display: flex;
  gap: 16px;
  margin-top: var(--spacing-4);
`;

const SocialIcon = styled(Button)`
  background: rgba(255, 255, 255, 0.15) !important;
  color: white !important;
  border: none !important;
  width: 44px !important;
  height: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 1.2rem !important;
  transition: all 0.3s ease !important;
  border-radius: var(--border-radius-full) !important;
  backdrop-filter: blur(5px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);

  &:hover {
    background: ${props => props.color || 'var(--color-accent-1)'} !important;
    transform: translateY(-3px);
  }
`;

// Quick links
const QuickLinks = styled(List)`
  .ant-list-item {
    border-bottom: none !important;
    padding: 10px 0 !important;
    transition: all 0.3s ease;

    &:hover {
      transform: translateX(5px);
    }
  }

  .ant-list-item-meta-title {
    margin-bottom: 0 !important;
  }

  .ant-list-item-meta-avatar {
    font-size: 1.1rem;
    color: var(--color-secondary);
  }
`;

// Contact info
const ContactInfo = styled(List)`
  .ant-list-item {
    border-bottom: none !important;
    padding: 12px 0 !important;
    transition: all 0.3s ease;
  }

  .ant-list-item-meta-avatar {
    font-size: 1.2rem;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: var(--border-radius-full);
    background: rgba(255, 255, 255, 0.1);
    margin-right: 12px;
  }

  .ant-list-item-meta-title {
    color: white !important;
    font-weight: 600 !important;
    margin-bottom: 4px !important;
  }

  .ant-list-item-meta-description {
    color: rgba(255, 255, 255, 0.8) !important;
  }
`;

// Newsletter
const NewsletterCard = styled(Card)`
  background: rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  border-radius: var(--border-radius-lg) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid transparent;
    border-radius: var(--border-radius-lg);
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .ant-card-body {
    padding: var(--spacing-6) !important;
  }
`;

const SubscribeInput = styled(Input.Search)`
  .ant-input {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    height: 48px;
    border-radius: var(--border-radius-full) 0 0 var(--border-radius-full) !important;
    padding-left: 20px;

    &::placeholder {
      color: rgba(255, 255, 255, 0.6) !important;
    }

    &:focus,
    &:hover {
      border-color: var(--color-secondary) !important;
    }
  }

  .ant-input-search-button {
    background: var(--gradient-secondary) !important;
    border-color: var(--color-secondary) !important;
    height: 48px !important;
    width: 48px !important;
    border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    &:hover {
      background: var(--color-secondary-dark) !important;
      border-color: var(--color-secondary-dark) !important;
    }

    .anticon {
      font-size: 1.2rem;
    }
  }
`;

const FooterLink = styled(Text)`
  color: rgba(255, 255, 255, 0.9) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;

  .anticon {
    opacity: 0;
    transform: translateX(-5px);
    transition: all 0.2s ease;
  }

  &:hover {
    color: var(--color-secondary) !important;

    .anticon {
      opacity: 1;
      transform: translateX(0);
    }
  }
`;

const Footer = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();

  const handleNavigation = (path) => {
    navigate(path);
  };

  // Quick links data
  const quickLinks = [
    { icon: <HomeOutlined />, title: "Home", path: "/" },
    { icon: <InfoCircleOutlined />, title: "About Us", path: "/about" },
    {
      icon: <ShoppingOutlined />,
      title: "Manufacturing",
      path: "/manufacturing",
    },
    { icon: <ToolOutlined />, title: "Workshop", path: "/workshop" },
    {
      icon: <ExperimentOutlined />,
      title: "Craftsmanship",
      path: "/craftsmanship",
    },
    { icon: <ContactsOutlined />, title: "Contact Us", path: "/contact" },
  ];

  // Contact info data
  const contactInfo = [
    {
      icon: <EnvironmentOutlined />,
      title: "Our Location",
      description: "Ramavatargems, Johari Bazaar, Jaipur, Rajasthan",
      color: "var(--color-accent-1)"
    },
    {
      icon: <PhoneOutlined />,
      title: "Phone Number",
      description: "94631 96935",
      color: "var(--color-accent-2)"
    },
    {
      icon: <MailOutlined />,
      title: "Email Address",
      description: "<EMAIL>",
      color: "var(--color-accent-3)"
    },
    {
      icon: <ClockCircleOutlined />,
      title: "Business Hours",
      description: "Monday - Saturday: 10:00 AM - 8:00 PM",
      color: "var(--color-accent-4)"
    },
  ];

  const handleSubscribe = (value) => {
    console.log("Subscribed with email:", value);
    // In a real app, you would send this to your backend
    form.resetFields();
  };

  return (
    <StyledFooter>
      <TopFooter>
        <FooterContainer>
          <Row gutter={[32, 48]}>
            {/* About Section */}
            <Col xs={24} sm={24} md={8} lg={8}>
              <LogoSection>
                <RamavatargemsLogo width={180} />
              </LogoSection>

              <FooterText>
                Diamond Manufacturing Excellence since 1982. Our Jaipur-based
                legacy continues with master craftsmanship and precision
                techniques that transform rough diamonds into brilliant
                masterpieces.
              </FooterText>

              <SocialIcons>
                <SocialIcon shape="circle" icon={<FacebookOutlined />} color="var(--color-accent-1)" />
                <SocialIcon shape="circle" icon={<InstagramOutlined />} color="var(--color-accent-2)" />
                <SocialIcon shape="circle" icon={<TwitterOutlined />} color="var(--color-accent-3)" />
              </SocialIcons>
            </Col>

            {/* Quick Links Section */}
            <Col xs={24} sm={12} md={8} lg={5}>
              <FooterTitle level={4}>Quick Links</FooterTitle>
              <QuickLinks
                dataSource={quickLinks}
                renderItem={(item) => (
                  <List.Item
                    onClick={() => handleNavigation(item.path)}
                    style={{ cursor: "pointer" }}
                  >
                    <List.Item.Meta
                      avatar={item.icon}
                      title={
                        <FooterLink>
                          {item.title} <RightOutlined style={{ marginLeft: 5 }} />
                        </FooterLink>
                      }
                    />
                  </List.Item>
                )}
              />
            </Col>

            {/* Contact Section */}
            <Col xs={24} sm={12} md={8} lg={6}>
              <FooterTitle level={4}>Contact Us</FooterTitle>
              <ContactInfo
                dataSource={contactInfo}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<div style={{ color: item.color }}>{item.icon}</div>}
                      title={item.title}
                      description={item.description}
                    />
                  </List.Item>
                )}
              />
            </Col>

            {/* Newsletter Section */}
            <Col xs={24} sm={24} md={24} lg={5}>
              <FooterTitle level={4}>Newsletter</FooterTitle>
              <NewsletterCard>
                <FooterText>
                  Subscribe to our newsletter to receive updates on new
                  collections and special offers.
                </FooterText>
                <Form form={form}>
                  <Form.Item name="email">
                    <SubscribeInput
                      placeholder="Your email address"
                      enterButton={<SendOutlined />}
                      onSearch={handleSubscribe}
                    />
                  </Form.Item>
                </Form>
              </NewsletterCard>
            </Col>
          </Row>
        </FooterContainer>
      </TopFooter>

      <BottomFooter>
        <FooterContainer>
          <Row justify="space-between" align="middle">
            <Col xs={24} md={12} style={{ textAlign: 'center', marginBottom: { xs: '10px', md: '0' } }}>
              <Text style={{ color: "rgba(255, 255, 255, 0.8)" }}>
                &copy; {new Date().getFullYear()} Ramavatargems. All Rights
                Reserved.
              </Text>
            </Col>
            <Col xs={24} md={12} style={{ textAlign: { xs: 'center', md: 'right' } }}>
              <Space split={<Divider type="vertical" style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />}>
                <Text style={{ color: "rgba(255, 255, 255, 0.8)", cursor: 'pointer' }}>Privacy Policy</Text>
                <Text style={{ color: "rgba(255, 255, 255, 0.8)", cursor: 'pointer' }}>Terms of Service</Text>
                <Text style={{ color: "rgba(255, 255, 255, 0.8)", cursor: 'pointer' }}>Sitemap</Text>
              </Space>
            </Col>
          </Row>
        </FooterContainer>
      </BottomFooter>
    </StyledFooter>
  );
};

export default Footer;
