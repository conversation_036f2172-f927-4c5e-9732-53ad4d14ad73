/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Dancing+Script:wght@400;500;600;700&display=swap');

:root {
  /* Colorful Theme Colors */
  --color-primary: #6366F1; /* Indigo */
  --color-primary-light: #818CF8; /* Light indigo */
  --color-primary-dark: #4F46E5; /* Dark indigo */
  
  --color-secondary: #F59E0B; /* Amber */
  --color-secondary-light: #FBBF24; /* Light amber */
  --color-secondary-dark: #D97706; /* Dark amber */
  
  --color-accent-1: #EC4899; /* Pink */
  --color-accent-2: #10B981; /* Emerald */
  --color-accent-3: #3B82F6; /* Blue */
  --color-accent-4: #8B5CF6; /* Violet */
  
  --color-text: #1E293B; /* Deep slate for main text */
  --color-text-light: #475569; /* Medium slate for secondary text */
  --color-text-lighter: #64748B; /* Light slate for tertiary text */
  
  --color-background: #FFFFFF; /* Pure white */
  --color-background-alt: #F8FAFC; /* Very light gray */
  --color-background-card: #FFFFFF; /* White for cards */
  
  --color-border: #E2E8F0; /* Light gray for borders */
  --color-shadow: rgba(0, 0, 0, 0.1);
  
  /* Feedback Colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #6366F1 0%, #818CF8 100%);
  --gradient-secondary: linear-gradient(135deg, #F59E0B 0%, #FBBF24 100%);
  --gradient-accent-1: linear-gradient(135deg, #EC4899 0%, #F472B6 100%);
  --gradient-accent-2: linear-gradient(135deg, #10B981 0%, #34D399 100%);
  --gradient-accent-3: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
  --gradient-accent-4: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
  --gradient-rainbow: linear-gradient(to right, #6366F1, #EC4899, #F59E0B, #10B981, #3B82F6);
  
  /* Typography */
  --font-primary: 'Montserrat', sans-serif;
  --font-secondary: 'Playfair Display', serif;
  --font-accent: 'Dancing Script', cursive;
  
  /* Spacing */
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem; /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem; /* 24px */
  --spacing-8: 2rem; /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem; /* 48px */
  --spacing-16: 4rem; /* 64px */
  --spacing-20: 5rem; /* 80px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-colored-primary: 0 10px 15px rgba(99, 102, 241, 0.3);
  --shadow-colored-secondary: 0 10px 15px rgba(245, 158, 11, 0.3);
  
  /* Border Radius */
  --border-radius-sm: 0.25rem; /* 4px */
  --border-radius-md: 0.5rem; /* 8px */
  --border-radius-lg: 1rem; /* 16px */
  --border-radius-xl: 1.5rem; /* 24px */
  --border-radius-full: 9999px;
  
  /* Transitions */
  --transition-default: all 0.3s ease;
  --transition-fast: all 0.15s ease;
  --transition-slow: all 0.5s ease;
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
}

/* Dark Theme Class */
.dark-theme {
  --color-primary: #818CF8; /* Light indigo */
  --color-primary-light: #A5B4FC; /* Lighter indigo */
  --color-primary-dark: #6366F1; /* Indigo */
  
  --color-secondary: #FBBF24; /* Light amber */
  --color-secondary-light: #FCD34D; /* Lighter amber */
  --color-secondary-dark: #F59E0B; /* Amber */
  
  --color-text: #F8FAFC; /* Very light gray for main text */
  --color-text-light: #E2E8F0; /* Light gray for body text */
  --color-text-lighter: #CBD5E1; /* Lighter gray for tertiary text */
  
  --color-background: #0F172A; /* Very dark slate blue */
  --color-background-alt: #1E293B; /* Deep slate blue */
  --color-background-card: #1E293B; /* Deep slate blue for cards */
  
  --color-border: #334155; /* Lighter slate blue for borders */
  --color-shadow: rgba(0, 0, 0, 0.2);
  
  --shadow-colored-primary: 0 10px 15px rgba(129, 140, 248, 0.3);
  --shadow-colored-secondary: 0 10px 15px rgba(251, 191, 36, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  color: var(--color-text);
  line-height: 1.5;
  background-color: var(--color-background);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-default), color var(--transition-default);
  overflow-x: hidden;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--color-text);
}

h1 {
  font-size: 3rem;
  letter-spacing: -0.025em;
}

h2 {
  font-size: 2.25rem;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.875rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.625;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  cursor: pointer;
  font-family: var(--font-primary);
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section {
  padding: var(--spacing-16) 0;
}

.section-title {
  position: relative;
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-rainbow);
  border-radius: 3px;
}

.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-secondary {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-rainbow {
  background: var(--gradient-rainbow);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.card {
  background-color: var(--color-background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-default), box-shadow var(--transition-default);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .container {
    max-width: 1024px;
  }
}

@media (max-width: 1024px) {
  .container {
    max-width: 768px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  .section {
    padding: var(--spacing-12) 0;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 640px;
  }
  
  h1 {
    font-size: 2.25rem;
  }
  
  h2 {
    font-size: 1.875rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  .section {
    padding: var(--spacing-8) 0;
  }
}

@media (max-width: 640px) {
  .container {
    max-width: 100%;
    padding: 0 var(--spacing-4);
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
}
