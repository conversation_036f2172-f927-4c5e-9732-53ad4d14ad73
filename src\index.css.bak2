/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Montserrat:wght@300;400;500;600;700&family=Cormorant+Garamond:wght@400;500;600;700&display=swap');

:root {
  /* Light Theme Colors */
  --color-primary: #1E3A8A; /* Deep royal blue */
  --color-primary-light: #2563EB; /* Brighter blue */
  --color-primary-dark: #0F172A; /* Very dark blue */
  
  --color-secondary: #D4AF37; /* Classic gold */
  --color-secondary-light: #F7E9B7; /* Light gold */
  --color-secondary-dark: #9E7C1E; /* Darker gold */
  
  --color-text: #1E293B; /* Very dark gray for main text */
  --color-text-light: #475569; /* Dark gray for body text */
  --color-text-lighter: #64748B; /* Medium gray for secondary text */
  
  --color-background: #FFFFFF; /* Pure white */
  --color-background-alt: #F8FAFC; /* Very light gray */
  --color-background-card: #FFFFFF; /* White for cards */
  
  --color-border: #E2E8F0; /* Light gray for borders */
  --color-shadow: rgba(0, 0, 0, 0.1);
  
  /* Accent Colors */
  --color-accent-teal: #0D9488;
  --color-accent-purple: #7E22CE;
  --color-accent-rose: #BE185D;
  --color-accent-amber: #D97706;
  
  /* Feedback Colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Gradients */
  --gradient-blue-sapphire: linear-gradient(135deg, #1E3A8A 0%, #3B82F6 100%);
  --gradient-gold-diamond: linear-gradient(135deg, #D4AF37 0%, #F7E9B7 100%);
  --gradient-dark-sapphire: linear-gradient(135deg, #0F172A 0%, #1E3A8A 100%);
  --gradient-luxury-gold: linear-gradient(to right, #BF953F 0%, #FCF6BA 50%, #B38728 100%);
  --gradient-silver-platinum: linear-gradient(135deg, #D1D5DB 0%, #F8FAFC 100%);
  
  /* Overlays */
  --color-overlay-light: rgba(255, 255, 255, 0.8);
  --color-overlay-dark: rgba(15, 23, 42, 0.7);
  --color-overlay-blue: rgba(30, 58, 138, 0.7);
  --color-overlay-gold: rgba(212, 175, 55, 0.2);

  /* Typography */
  --font-heading: 'Playfair Display', serif;
  --font-body: 'Montserrat', sans-serif;
  --font-accent: 'Cormorant Garamond', serif;
  
  /* Spacing */
  --spacing-1: 0.25rem; /* 4px */
  --spacing-2: 0.5rem; /* 8px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem; /* 24px */
  --spacing-8: 2rem; /* 32px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-12: 3rem; /* 48px */
  --spacing-16: 4rem; /* 64px */
  --spacing-20: 5rem; /* 80px */
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-outline: 0 0 0 3px rgba(30, 58, 138, 0.5);
  --shadow-gold: 0 4px 20px rgba(212, 175, 55, 0.25);
  --shadow-blue: 0 4px 20px rgba(30, 58, 138, 0.25);
  
  /* Border Radius */
  --border-radius-sm: 0.125rem; /* 2px */
  --border-radius-md: 0.25rem; /* 4px */
  --border-radius-lg: 0.5rem; /* 8px */
  --border-radius-xl: 0.75rem; /* 12px */
  --border-radius-2xl: 1rem; /* 16px */
  --border-radius-full: 9999px;
  
  /* Transitions */
  --transition-default: all 0.3s ease;
  --transition-fast: all 0.15s ease;
  --transition-slow: all 0.5s ease;
  --transition-bezier: all 0.6s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Dark Theme Class */
.dark-theme {
  --color-primary: #3B82F6; /* Brighter blue for dark mode */
  --color-primary-light: #60A5FA; /* Even brighter blue */
  --color-primary-dark: #1E3A8A; /* Original blue */
  
  --color-secondary: #D4AF37; /* Keep gold the same */
  --color-secondary-light: #F7E9B7;
  --color-secondary-dark: #9E7C1E;
  
  --color-text: #F8FAFC; /* Very light gray for main text */
  --color-text-light: #E2E8F0; /* Light gray for body text */
  --color-text-lighter: #94A3B8; /* Medium gray for secondary text */
  
  --color-background: #0F172A; /* Very dark blue */
  --color-background-alt: #1E293B; /* Dark blue-gray */
  --color-background-card: #1E293B; /* Dark blue-gray for cards */
  
  --color-border: #334155; /* Dark gray for borders */
  --color-shadow: rgba(0, 0, 0, 0.3);
  
  /* Gradients for dark mode */
  --gradient-blue-sapphire: linear-gradient(135deg, #3B82F6 0%, #60A5FA 100%);
  --gradient-dark-sapphire: linear-gradient(135deg, #0F172A 0%, #1E3A8A 100%);
  
  /* Shadows for dark mode */
  --shadow-gold: 0 4px 20px rgba(212, 175, 55, 0.15);
  --shadow-blue: 0 4px 20px rgba(59, 130, 246, 0.25);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  color: var(--color-text);
  line-height: 1.5;
  background-color: var(--color-background);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-default), color var(--transition-default);
  overflow-x: hidden;
  min-height: 100vh;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--color-text);
}

h1 {
  font-size: 3rem;
  letter-spacing: -0.025em;
}

h2 {
  font-size: 2.25rem;
  letter-spacing: -0.025em;
}

h3 {
  font-size: 1.875rem;
}

h4 {
  font-size: 1.5rem;
}

h5 {
  font-size: 1.25rem;
}

h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.625;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  cursor: pointer;
  font-family: var(--font-body);
}

.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.section {
  padding: var(--spacing-16) 0;
}

.section-title {
  position: relative;
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--gradient-gold-diamond);
  border-radius: 3px;
}

.text-gradient-gold {
  background: var(--gradient-luxury-gold);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-blue {
  background: var(--gradient-blue-sapphire);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.card {
  background-color: var(--color-background-card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: transform var(--transition-default), box-shadow var(--transition-default);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .container {
    max-width: 1024px;
  }
}

@media (max-width: 1024px) {
  .container {
    max-width: 768px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
  
  .section {
    padding: var(--spacing-12) 0;
  }
}

@media (max-width: 768px) {
  .container {
    max-width: 640px;
  }
  
  h1 {
    font-size: 2.25rem;
  }
  
  h2 {
    font-size: 1.875rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  .section {
    padding: var(--spacing-8) 0;
  }
}

@media (max-width: 640px) {
  .container {
    max-width: 100%;
    padding: 0 var(--spacing-4);
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
}
