import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Layout,
  Menu,
  Button,
  Drawer,
  Typography,
  Row,
  Col,
  Space,
  Divider,
  Badge,
  Input,
} from "antd";
import {
  MenuOutlined,
  HomeOutlined,
  InfoCircleOutlined,
  AppstoreOutlined,
  MailOutlined,
  ShoppingOutlined,
  GoldOutlined,
  PhoneOutlined,
  SearchOutlined,
  CloseOutlined,
  FacebookOutlined,
  InstagramOutlined,
  TwitterOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import styled, { keyframes } from "styled-components";
import { useTheme } from "../context/ThemeContext";
import ThemeToggle from "./ThemeToggle";

const { Header } = Layout;
const { Title, Text } = Typography;
const { Search } = Input;

// Animations
const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
`;

const float = keyframes`
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
`;

// Styled components
const StyledHeader = styled(Header)`
  background-color: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? 'rgba(18, 18, 18, 0.85)' 
        : 'rgba(255, 255, 255, 0.85)' 
      : 'transparent'};
  backdrop-filter: ${props => props.scrolled ? 'blur(10px)' : 'none'};
  padding: 0;
  box-shadow: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? '0 5px 20px rgba(0, 0, 0, 0.3)' 
        : '0 5px 20px rgba(0, 0, 0, 0.08)' 
      : 'none'};
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  line-height: normal;
  height: auto;
  transition: all var(--transition-medium);
  
  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: ${props => props.scrolled ? '2px' : '0'};
    background: var(--gradient-gold);
    opacity: 0.8;
    transition: height var(--transition-medium);
  }
`;

const HeaderTop = styled.div`
  background: ${props => props.isDarkMode 
    ? 'linear-gradient(to right, #001018 0%, #001529 100%)' 
    : 'linear-gradient(to right, var(--color-secondary-dark) 0%, var(--color-secondary) 100%)'};
  padding: ${props => props.scrolled ? '6px 0' : '8px 0'};
  color: white;
  transition: all var(--transition-medium);
  
  @media (max-width: 768px) {
    padding: 6px 0;
  }
`;

const TopContainer = styled(Row)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
`;

const TopInfo = styled(Text)`
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  
  .anticon {
    margin-right: 8px;
    color: var(--color-primary-light);
  }
`;

const SocialIcons = styled.div`
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  
  @media (max-width: 576px) {
    display: none;
  }
`;

const SocialIcon = styled(Button)`
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: none !important;
  color: white !important;
  font-size: 1rem !important;
  border-radius: 50% !important;
  transition: all var(--transition-medium) !important;
  
  &:hover {
    background: var(--color-primary) !important;
    transform: translateY(-2px);
  }
`;

const MainHeader = styled.div`
  padding: ${props => props.scrolled ? '8px 0' : '16px 0'};
  background-color: ${props => 
    props.scrolled 
      ? 'transparent' 
      : props.isDarkMode 
        ? 'var(--color-background-dark-theme)' 
        : 'white'};
  transition: all var(--transition-medium);
  box-shadow: ${props => 
    !props.scrolled && props.isHome 
      ? props.isDarkMode 
        ? '0 2px 10px rgba(0, 0, 0, 0.2)' 
        : '0 2px 10px rgba(0, 0, 0, 0.05)' 
      : 'none'};
`;

const HeaderContainer = styled(Row)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: transform var(--transition-medium);
  animation: ${float} 6s ease-in-out infinite;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const LogoIcon = styled(GoldOutlined)`
  font-size: 2.2rem;
  color: var(--color-primary);
  margin-right: 12px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
`;

const Logo = styled(Title)`
  margin: 0 !important;
  font-family: var(--font-heading) !important;
  font-size: ${props => props.scrolled ? '1.8rem' : '2.2rem'} !important;
  font-weight: 700 !important;
  color: ${props => 
    props.isHome && !props.scrolled 
      ? 'white' 
      : props.isDarkMode 
        ? 'white' 
        : 'var(--color-secondary)'} !important;
  transition: all var(--transition-medium) !important;
  text-shadow: ${props => 
    props.isHome && !props.scrolled 
      ? '0 2px 4px rgba(0, 0, 0, 0.3)' 
      : props.isDarkMode 
        ? '0 1px 3px rgba(0, 0, 0, 0.3)' 
        : 'none'} !important;
  
  .primary-text {
    background: var(--gradient-gold);
    background-size: 200% auto;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: ${shimmer} 8s linear infinite;
    text-shadow: none !important;
  }
`;

const NavContainer = styled.div`
  background: ${props => 
    props.scrolled 
      ? 'transparent' 
      : props.isDarkMode 
        ? 'linear-gradient(to right, #001018 0%, #001529 100%)' 
        : 'linear-gradient(to right, var(--color-secondary-dark) 0%, var(--color-secondary) 100%)'};
  transition: all var(--transition-medium);
  border-top: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? '1px solid rgba(255, 255, 255, 0.05)' 
        : '1px solid rgba(0, 0, 0, 0.05)' 
      : 'none'};
`;

const MenuContainer = styled(Row)`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
`;

const StyledMenu = styled(Menu)`
  background: transparent;
  color: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? 'white' 
        : 'var(--color-text)' 
      : 'white'};
  border: none;
  width: 100%;
  line-height: 46px;
  transition: all var(--transition-medium);
  
  .ant-menu-item {
    padding: 0 24px;
    margin: 0 4px;
    color: ${props => 
      props.scrolled 
        ? props.isDarkMode 
          ? 'rgba(255, 255, 255, 0.9)' 
          : 'var(--color-text)' 
        : 'rgba(255, 255, 255, 0.9)'};
    font-size: 1rem;
    font-weight: 500;
    position: relative;
    transition: all var(--transition-medium);
    text-shadow: ${props => 
      props.scrolled 
        ? 'none' 
        : '0 1px 2px rgba(0, 0, 0, 0.2)'};
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--gradient-gold);
      transition: all var(--transition-medium);
      transform: translateX(-50%);
      opacity: 0;
    }
    
    &:hover {
      color: var(--color-primary);
      text-shadow: none;
      
      &::after {
        width: 30px;
        opacity: 1;
      }
    }
    
    &.ant-menu-item-selected {
      background-color: ${props => 
        props.scrolled 
          ? props.isDarkMode 
            ? 'rgba(156, 102, 68, 0.2)' 
            : 'rgba(156, 102, 68, 0.1)' 
          : 'rgba(156, 102, 68, 0.2)'};
      color: var(--color-primary);
      font-weight: 600;
      text-shadow: none;
      
      &::after {
        width: 30px;
        opacity: 1;
      }
    }
    
    .anticon {
      margin-right: 8px;
      font-size: 1.1rem;
    }
  }
  
  @media (max-width: 768px) {
    display: none;
  }
`;

const ActionButtons = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: flex-end;
`;

const SearchButton = styled(Button)`
  background: transparent !important;
  border: none !important;
  color: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? 'white' 
        : 'var(--color-text)' 
      : 'white'} !important;
  font-size: 1.2rem !important;
  padding: 0 12px !important;
  height: 46px !important;
  transition: all var(--transition-medium) !important;
  text-shadow: ${props => 
    props.scrolled 
      ? 'none' 
      : '0 1px 2px rgba(0, 0, 0, 0.2)'};
  
  &:hover {
    color: var(--color-primary) !important;
    transform: scale(1.1);
    text-shadow: none;
  }
`;

const MobileMenuButton = styled(Button)`
  display: none;
  background-color: ${props => 
    props.scrolled 
      ? props.isDarkMode 
        ? 'rgba(156, 102, 68, 0.2)' 
        : 'var(--color-primary)' 
      : 'rgba(255, 255, 255, 0.1)'};
  color: ${props => 
    props.scrolled && !props.isDarkMode 
      ? 'white' 
      : 'white'};
  border: none;
  height: 46px;
  width: 46px;
  transition: all var(--transition-medium);
  
  &:hover,
  &:focus {
    background-color: var(--color-primary);
    color: white;
    transform: scale(1.05);
  }
  
  @media (max-width: 768px) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
`;

const StyledDrawer = styled(Drawer)`
  .ant-drawer-header {
    background: ${props => props.isDarkMode 
      ? 'linear-gradient(135deg, #001018 0%, #001529 100%)' 
      : 'linear-gradient(135deg, var(--color-secondary-dark) 0%, var(--color-secondary) 100%)'};
    border-bottom: none;
    padding: 16px 24px;
  }
  
  .ant-drawer-title {
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    display: flex;
    align-items: center;
    
    .logo-icon {
      margin-right: 12px;
      font-size: 1.8rem;
      color: var(--color-primary);
    }
    
    .primary-text {
      color: var(--color-primary);
    }
  }
  
  .ant-drawer-close {
    color: white;
    font-size: 1.2rem;
    
    &:hover {
      color: var(--color-primary-light);
    }
  }
  
  .ant-drawer-body {
    padding: 0;
    background-color: ${props => props.isDarkMode ? 'var(--color-background-dark-theme)' : '#f9f7f3'};
  }
  
  .ant-menu {
    background-color: transparent;
    border-right: none;
    
    .ant-menu-item {
      font-size: 1.1rem;
      height: 60px;
      line-height: 60px;
      margin: 0;
      padding: 0 24px;
      border-bottom: 1px solid ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};
      transition: all var(--transition-medium);
      color: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.9)' : 'var(--color-text)'};
      
      .anticon {
        font-size: 1.2rem;
        margin-right: 12px;
        color: var(--color-primary);
      }
      
      &:hover {
        background-color: ${props => props.isDarkMode ? 'rgba(156, 102, 68, 0.1)' : 'rgba(156, 102, 68, 0.05)'};
      }
      
      &.ant-menu-item-selected {
        background-color: ${props => props.isDarkMode ? 'rgba(156, 102, 68, 0.2)' : 'rgba(156, 102, 68, 0.1)'};
        color: var(--color-primary);
        font-weight: 600;
      }
    }
  }
  
  .drawer-footer {
    padding: 24px;
    border-top: 1px solid ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)'};
    
    .contact-info {
      margin-bottom: 16px;
      
      .title {
        font-weight: 600;
        margin-bottom: 8px;
        color: ${props => props.isDarkMode ? 'white' : 'var(--color-secondary)'};
      }
      
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.8)' : 'var(--color-text)'};
        
        .anticon {
          margin-right: 8px;
          color: var(--color-primary);
        }
      }
    }
    
    .social-icons {
      display: flex;
      gap: 12px;
      margin-top: 16px;
      
      .social-icon {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: ${props => props.isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'var(--color-secondary)'};
        color: white;
        border-radius: 50%;
        font-size: 1.2rem;
        transition: all var(--transition-medium);
        
        &:hover {
          background-color: var(--color-primary);
          transform: translateY(-2px);
        }
      }
    }
  }
`;
