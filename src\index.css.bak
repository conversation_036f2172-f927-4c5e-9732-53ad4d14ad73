:root {
  /* Light Theme Colors */
  --color-primary-light-theme: #8b4513; /* Darker brown for better contrast */
  --color-primary-light-light-theme: #d4b996;
  --color-primary-dark-light-theme: #5d2906; /* Even darker for hover states */
  --color-secondary-light-theme: #001529; /* Ant Design dark blue */
  --color-text-light-theme: #262626; /* Darker text for better readability */
  --color-text-light-light-theme: #4d4d4d; /* Darker gray for better contrast */
  --color-background-light-theme: #ffffff;
  --color-background-alt-light-theme: #f5f5f5; /* Slightly darker for better contrast */
  --color-accent-light-theme: #b8860b; /* Dark goldenrod for accents */
  --color-border-light-theme: #f0f0f0;
  --color-card-light-theme: #ffffff;
  --color-card-hover-light-theme: #f9f9f9;
  --color-shadow-light-theme: rgba(0, 0, 0, 0.1);
  --color-overlay-light-theme: rgba(0, 0, 0, 0.5);

  /* Dark Theme Colors */
  --color-primary-dark-theme: #d4b996; /* Lighter gold for dark mode */
  --color-primary-light-dark-theme: #e6d2b8; /* Even lighter for hover */
  --color-primary-dark-dark-theme: #8b4513; /* Original brown for accents */
  --color-secondary-dark-theme: #001529; /* Keep the dark blue */
  --color-text-dark-theme: #e6e6e6; /* Light text for dark mode */
  --color-text-light-dark-theme: #b3b3b3; /* Lighter gray for secondary text */
  --color-background-dark-theme: #121212; /* Dark background */
  --color-background-alt-dark-theme: #1f1f1f; /* Slightly lighter dark background */
  --color-accent-dark-theme: #d4af37; /* Brighter gold for accents */
  --color-border-dark-theme: #333333;
  --color-card-dark-theme: #1f1f1f;
  --color-card-hover-dark-theme: #2a2a2a;
  --color-shadow-dark-theme: rgba(0, 0, 0, 0.3);
  --color-overlay-dark-theme: rgba(0, 0, 0, 0.7);

  /* Default to light theme */
  --color-primary: var(--color-primary-light-theme);
  --color-primary-light: var(--color-primary-light-light-theme);
  --color-primary-dark: var(--color-primary-dark-light-theme);
  --color-secondary: var(--color-secondary-light-theme);
  --color-text: var(--color-text-light-theme);
  --color-text-light: var(--color-text-light-light-theme);
  --color-background: var(--color-background-light-theme);
  --color-background-alt: var(--color-background-alt-light-theme);
  --color-accent: var(--color-accent-light-theme);
  --color-border: var(--color-border-light-theme);
  --color-card: var(--color-card-light-theme);
  --color-card-hover: var(--color-card-hover-light-theme);
  --color-shadow: var(--color-shadow-light-theme);
  --color-overlay: var(--color-overlay-light-theme);

  /* Gradients */
  --gradient-primary-light: linear-gradient(
    135deg,
    var(--color-primary) 0%,
    var(--color-primary-light) 100%
  );
  --gradient-primary-dark: linear-gradient(
    135deg,
    var(--color-primary-dark) 0%,
    var(--color-primary) 100%
  );
  --gradient-secondary: linear-gradient(
    135deg,
    var(--color-secondary) 0%,
    #002b56 100%
  );
  --gradient-gold: linear-gradient(
    to right,
    #bf953f 0%,
    #fcf6ba 50%,
    #b38728 100%
  );

  /* Typography */
  --font-heading: "Playfair Display", serif;
  --font-body: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* Ant Design font stack */

  /* Spacing - Ant Design uses 8px as base unit */
  --spacing-xs: 0.5rem; /* 8px */
  --spacing-sm: 1rem; /* 16px */
  --spacing-md: 1.5rem; /* 24px */
  --spacing-lg: 2rem; /* 32px */
  --spacing-xl: 3rem; /* 48px */

  /* Borders */
  --border-radius: 4px; /* Slightly larger for better aesthetics */
  --border-radius-lg: 8px;
  --border-radius-xl: 16px;
  --border-width: 1px;
  --border-color: var(--color-border);

  /* Animations */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Shadows */
  --shadow-sm: 0 2px 8px var(--color-shadow);
  --shadow-md: 0 4px 16px var(--color-shadow);
  --shadow-lg: 0 8px 24px var(--color-shadow);

  /* Z-indices */
  --z-index-dropdown: 100;
  --z-index-sticky: 200;
  --z-index-fixed: 300;
  --z-index-modal-backdrop: 400;
  --z-index-modal: 500;
  --z-index-popover: 600;
  --z-index-tooltip: 700;
}

/* Dark Theme Class */
.dark-theme {
  --color-primary: var(--color-primary-dark-theme);
  --color-primary-light: var(--color-primary-light-dark-theme);
  --color-primary-dark: var(--color-primary-dark-dark-theme);
  --color-secondary: var(--color-secondary-dark-theme);
  --color-text: var(--color-text-dark-theme);
  --color-text-light: var(--color-text-light-dark-theme);
  --color-background: var(--color-background-dark-theme);
  --color-background-alt: var(--color-background-alt-dark-theme);
  --color-accent: var(--color-accent-dark-theme);
  --color-border: var(--color-border-dark-theme);
  --color-card: var(--color-card-dark-theme);
  --color-card-hover: var(--color-card-hover-dark-theme);
  --color-shadow: var(--color-shadow-dark-theme);
  --color-overlay: var(--color-overlay-dark-theme);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  color: var(--color-text);
  line-height: 1.6;
  background-color: var(--color-background);
  font-size: 16px; /* Base font size */
  font-weight: 400; /* Regular weight for better readability */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-medium),
    color var(--transition-medium);
  overflow-x: hidden;
  min-height: 100vh;
  scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading);
  font-weight: 700; /* Bolder for better visibility */
  line-height: 1.3;
  margin-bottom: var(--spacing-sm);
  color: var(--color-secondary);
  letter-spacing: -0.02em; /* Slight letter spacing adjustment for readability */
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5); /* Subtle text shadow for contrast */
}

h1 {
  font-size: 3rem;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 2rem;
}

h4 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-sm);
  font-size: 1rem;
  line-height: 1.7; /* Increased line height for better readability */
  color: var(--color-text); /* Ensure paragraphs use the main text color */
  max-width: 70ch; /* Limit line length for better readability */
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--color-primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

button {
  cursor: pointer;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-sm);
}

.section {
  padding: var(--spacing-lg) 0;
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.75rem;
  }

  .section {
    padding: var(--spacing-md) 0;
  }
}
